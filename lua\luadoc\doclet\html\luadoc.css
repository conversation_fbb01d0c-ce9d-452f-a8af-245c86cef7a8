body { 
    margin-left: 1em; 
    margin-right: 1em; 
    font-family: arial, helvetica, geneva, sans-serif;
	background-color:#ffffff; margin:0px;
}

code {
    font-family: "Andale Mono", monospace; 
}

tt {
    font-family: "Andale Mono", monospace; 
}

body, td, th { font-size: 11pt; }

h1, h2, h3, h4 { margin-left: 0em; }

textarea, pre, tt { font-size:10pt; }
body, td, th { color:#000000; }
small { font-size:0.85em; }
h1 { font-size:1.5em; }
h2 { font-size:1.25em; }
h3 { font-size:1.15em; }
h4 { font-size:1.06em; }

a:link { font-weight:bold; color: #004080; text-decoration: none; }
a:visited { font-weight:bold; color: #006699; text-decoration: none; }
a:link:hover { text-decoration:underline; }
hr { color:#cccccc }
img { border-width: 0px; }


h3 { padding-top: 1em; }

p { margin-left: 1em; }

p.name { 
    font-family: "Andale Mono", monospace; 
    padding-top: 1em;
    margin-left: 0em; 
}

blockquote { margin-left: 3em; }

pre.example {
    background-color: rgb(245, 245, 245);
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: silver;
    border-right-color: silver;
    border-bottom-color: silver;
    border-left-color: silver;
    padding: 1em;
    margin-left: 1em;
    margin-right: 1em;
    font-family: "Andale Mono", monospace; 
    font-size: smaller;
}


hr { 
    margin-left: 0em;
	background: #00007f; 
	border: 0px;
	height: 1px;
}

ul { list-style-type: disc; }

table.index { border: 1px #00007f; }
table.index td { text-align: left; vertical-align: top; }
table.index ul { padding-top: 0em; margin-top: 0em; }

table {
    border: 1px solid black;
	border-collapse: collapse;
    margin-left: auto;
    margin-right: auto;
}
th {
    border: 1px solid black;
    padding: 0.5em;
}
td {
    border: 1px solid black;
    padding: 0.5em;
}
div.header, div.footer { margin-left: 0em; }

#container
{
	margin-left: 1em;
	margin-right: 1em;
	background-color: #f0f0f0;
}

#product
{
	text-align: center;
	border-bottom: 1px solid #cccccc;
	background-color: #ffffff;
}

#product big {
	font-size: 2em;
}

#product_logo
{
}

#product_name
{
}

#product_description
{
}

#main
{
	background-color: #f0f0f0;
	border-left: 2px solid #cccccc;
}

#navigation
{
	float: left;
	width: 18em;
	margin: 0;
	vertical-align: top;
	background-color: #f0f0f0;
	overflow:visible;
}

#navigation h1 {
	background-color:#e7e7e7;
	font-size:1.1em;
	color:#000000;
	text-align:left;
	margin:0px;
	padding:0.2em;
	border-top:1px solid #dddddd;
	border-bottom:1px solid #dddddd;
}

#navigation ul
{
	font-size:1em;
	list-style-type: none;
	padding: 0;
	margin: 1px;
}

#navigation li
{
	text-indent: -1em;
	margin: 0em 0em 0em 0.5em;
	display: block;
	padding: 3px 0px 0px 12px;
}

#navigation li li a
{
	padding: 0px 3px 0px -1em;
}

#content
{
	margin-left: 18em;
	padding: 1em;
	border-left: 2px solid #cccccc;
	border-right: 2px solid #cccccc;
	background-color: #ffffff;
}

#about
{
	clear: both;
	margin: 0;
	padding: 5px;
	border-top: 2px solid #cccccc;
	background-color: #ffffff;
}

@media print {
	body { 
		font: 12pt "Times New Roman", "TimeNR", Times, serif;
	}
	a { font-weight:bold; color: #004080; text-decoration: underline; }
	
	#main
	{
		background-color: #ffffff;
		border-left: 0px;
	}
	
	#container
	{
		margin-left: 2%;
		margin-right: 2%;
		background-color: #ffffff;
	}
	
	#content
	{
		margin-left: 0px;
		padding: 1em;
		border-left: 0px;
		border-right: 0px;
		background-color: #ffffff;
	}
	
	#navigation
	{
		display: none;
	}
	pre.example {
		font-family: "Andale Mono", monospace; 
		font-size: 10pt;
		page-break-inside: avoid;
	}
}

table.module_list td
{
	border-width: 1px;
	padding: 3px;
	border-style: solid;
	border-color: #cccccc;
}
table.module_list td.name { background-color: #f0f0f0; }
table.module_list td.summary { width: 100%; }

table.file_list
{
	border-width: 1px;
	border-style: solid;
	border-color: #cccccc;
	border-collapse: collapse;
}
table.file_list td
{
	border-width: 1px;
	padding: 3px;
	border-style: solid;
	border-color: #cccccc;
}
table.file_list td.name { background-color: #f0f0f0; }
table.file_list td.summary { width: 100%; }


table.function_list
{
	border-width: 1px;
	border-style: solid;
	border-color: #cccccc;
	border-collapse: collapse;
}
table.function_list td
{
	border-width: 1px;
	padding: 3px;
	border-style: solid;
	border-color: #cccccc;
}
table.function_list td.name { background-color: #f0f0f0; }
table.function_list td.summary { width: 100%; }


table.table_list
{
	border-width: 1px;
	border-style: solid;
	border-color: #cccccc;
	border-collapse: collapse;
}
table.table_list td
{
	border-width: 1px;
	padding: 3px;
	border-style: solid;
	border-color: #cccccc;
}
table.table_list td.name { background-color: #f0f0f0; }
table.table_list td.summary { width: 100%; }

dl.function dt {border-top: 1px solid #ccc; padding-top: 1em;}
dl.function dd {padding-bottom: 1em;}
dl.function h3 {padding-top: 1em; margin: 0; font-size: medium;}

dl.table dt {border-top: 1px solid #ccc; padding-top: 1em;}
dl.table dd {padding-bottom: 1em;}
dl.table h3 {padding: 0; margin: 0; font-size: medium;}

#TODO: make module_list, file_list, function_list, table_list inherit from a list

