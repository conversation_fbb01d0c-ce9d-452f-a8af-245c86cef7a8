# MUD客户端

## 项目说明

这是一个基于Lua的MUD客户端，支持与MUD服务器进行交互。

## 文件说明

- `mudclient.lua` - 主MUD客户端程序（自动检测luasocket）
- `client.lua` - 完整版MUD客户端（需要luasocket）
- `simple_client.lua` - 简化版MUD客户端（不需要luasocket）
- `network.lua` - 网络连接模块
- `config.lua` - 配置文件模块
- `start.lua` - 启动脚本
- `start.bat` - Windows批处理启动文件
- `test_socket.lua` - 测试luasocket是否可用
- `test_client.lua` - 测试客户端功能
- `sc/main.lua` - 脚本系统入口
- `sc/` - 脚本目录，包含各种MUD功能模块

## 使用方法

### 方法1: 使用批处理文件启动
```
双击 start.bat
```

### 方法2: 使用Lua直接启动
```
lua.exe start.lua
```

### 方法3: 直接运行主客户端
```
lua.exe mudclient.lua
```

### 方法4: 使用简化版客户端
```
lua.exe simple_client.lua
```

### 方法5: 测试客户端功能
```
lua.exe test_client.lua
```

## 依赖要求

- Lua 5.1 32位
- luasocket 32位 (可选，用于完整版客户端)

## 编码

所有文件使用GB2312编码。

## 功能特性

- 支持连接MUD服务器
- 命令发送和接收
- 触发器系统
- 别名系统
- 定时器系统
- 脚本扩展支持

## 命令说明

- `connect` - 连接到服务器
- `disconnect` - 断开连接
- `quit` 或 `exit` - 退出程序
- `help` - 显示帮助信息

## 配置

默认服务器: pkuxkx.net:8080
可以在代码中修改 `client.host` 和 `client.port` 来更改默认服务器。

## 注意事项

1. 如果没有luasocket，请使用简化版客户端
2. 确保所有文件使用GB2312编码
3. 脚本系统位于sc目录下，可以根据需要扩展功能
