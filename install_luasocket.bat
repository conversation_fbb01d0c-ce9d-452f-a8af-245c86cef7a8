@echo off
chcp 936
echo 正在安装luasocket...
echo.

echo 方法1: 从GitHub下载预编译版本
echo 请访问: https://github.com/lunarmodules/luasocket/releases
echo 下载适合Lua 5.1 32位的版本
echo.

echo 方法2: 使用LuaRocks安装
echo 如果你有LuaRocks，运行: luarocks install luasocket
echo.

echo 方法3: 手动下载文件
echo 需要下载以下文件到当前目录:
echo - socket.dll (或socket目录)
echo - mime.dll
echo - socket.lua
echo.

echo 下载地址建议:
echo https://luabinaries.sourceforge.net/
echo 或者
echo https://github.com/lunarmodules/luasocket
echo.

echo 下载完成后，将文件放到当前目录，然后运行 test_socket.lua 测试
pause
