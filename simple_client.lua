-- 简单MUD客户端
-- 编码: GB2312
-- 不依赖luasocket的基础版本

-- 全局变量
local client = {}
client.connected = false
client.host = "pkuxkx.net"
client.port = 8080
client.total_bytes = 0
client.running = true

-- 模拟连接状态
function Connect(host, port)
    host = host or client.host
    port = port or client.port
    
    client.host = host
    client.port = port
    
    Echo("正在连接到 " .. host .. ":" .. port .. "...")
    
    -- 这里需要实际的socket连接代码
    -- 暂时模拟连接成功
    client.connected = true
    Echo("连接成功! (模拟)")
    
    return true
end

-- 断开连接
function Disconnect()
    client.connected = false
    Echo("连接已断开")
end

-- 检查连接状态
function IsConnect()
    return client.connected
end

-- 发送数据到服务器
function Send(data)
    if not client.connected then
        Echo("未连接到服务器")
        return false
    end
    
    if not data then
        return false
    end
    
    Echo("发送: " .. data)
    return true
end

-- 获取接收的总字节数
function GameBytes()
    return client.total_bytes
end

-- 显示文本
function Echo(text)
    if text then
        print(text)
    end
end

-- 调试输出
function Print(text)
    if text then
        print("[DEBUG] " .. text)
    end
end

-- 模拟接收数据
function SimulateReceive()
    if client.connected then
        -- 模拟服务器发送欢迎信息
        local welcome_msg = "欢迎来到MUD世界!"
        if OnReceive then
            OnReceive(welcome_msg, welcome_msg)
        else
            Echo("服务器: " .. welcome_msg)
        end
    end
end

-- 处理用户输入
function ProcessInput()
    Echo("请输入命令 (输入 'help' 查看帮助):")
    local input = io.read()
    
    if input then
        if input == "quit" or input == "exit" then
            client.running = false
            return
        elseif input == "connect" then
            Connect()
            return
        elseif input == "disconnect" then
            Disconnect()
            return
        elseif input == "help" then
            Echo("可用命令:")
            Echo("  connect    - 连接到服务器")
            Echo("  disconnect - 断开连接")
            Echo("  quit/exit  - 退出程序")
            Echo("  help       - 显示帮助")
            return
        end
        
        -- 调用发送处理函数
        if OnSend then
            local result = OnSend(input)
            if result ~= false then
                Send(input)
            end
        else
            Send(input)
        end
    end
end

-- 主循环
function MainLoop()
    Echo("=== 简单MUD客户端 ===")
    Echo("版本: 1.0 (基础版)")
    Echo("编码: GB2312")
    Echo("")
    Echo("输入 'help' 查看可用命令")
    Echo("")
    
    while client.running do
        ProcessInput()
        
        -- 模拟接收数据
        if client.connected then
            -- 这里可以添加实际的网络接收代码
        end
    end
    
    if client.connected then
        Disconnect()
    end
    
    Echo("程序退出")
end

-- 加载脚本系统
function LoadScripts()
    Echo("正在加载脚本系统...")
    
    local success, err = pcall(function()
        dofile("sc/main.lua")
    end)
    
    if not success then
        Echo("加载脚本失败: " .. (err or "未知错误"))
        Echo("将以基础模式运行")
    else
        Echo("脚本系统加载成功")
    end
end

-- 程序入口
function StartClient()
    Echo("正在初始化MUD客户端...")
    
    -- 加载脚本系统
    LoadScripts()
    
    -- 启动主循环
    MainLoop()
end

-- 如果直接运行此文件，启动客户端
if arg and arg[0] and string.match(arg[0], "simple_client%.lua$") then
    StartClient()
end

return client
