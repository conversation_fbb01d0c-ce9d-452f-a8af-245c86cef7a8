-- MUD客户端配置文件
-- 编码: GB2312

local config = {}

-- 服务器配置
config.server = {
    host = "pkuxkx.net",    -- 默认服务器地址
    port = 8080,            -- 默认端口
    timeout = 10,           -- 连接超时时间（秒）
    encoding = "gb2312"     -- 字符编码
}

-- 客户端配置
config.client = {
    debug = false,          -- 调试模式
    auto_connect = false,   -- 自动连接
    buffer_size = 8192,     -- 接收缓冲区大小
    max_lines = 10000       -- 最大保存行数
}

-- 界面配置
config.ui = {
    show_debug = true,      -- 显示调试信息
    show_bytes = true,      -- 显示字节计数
    prompt = "> ",          -- 命令提示符
    welcome_msg = "欢迎使用MUD客户端!"
}

-- 脚本配置
config.scripts = {
    auto_load = true,       -- 自动加载脚本
    script_path = "sc/",    -- 脚本目录
    main_script = "main.lua" -- 主脚本文件
}

-- 日志配置
config.log = {
    enable = false,         -- 启用日志
    file = "mudclient.log", -- 日志文件
    level = "info"          -- 日志级别
}

-- 获取配置值
function config.get(section, key, default)
    if config[section] and config[section][key] ~= nil then
        return config[section][key]
    else
        return default
    end
end

-- 设置配置值
function config.set(section, key, value)
    if not config[section] then
        config[section] = {}
    end
    config[section][key] = value
end

-- 加载配置文件
function config.load(filename)
    filename = filename or "config.txt"
    local file = io.open(filename, "r")
    if file then
        for line in file:lines() do
            -- 解析配置行 section.key=value
            local section, key, value = string.match(line, "^([^.]+)%.([^=]+)=(.+)$")
            if section and key and value then
                -- 尝试转换为数字或布尔值
                local num_value = tonumber(value)
                if num_value then
                    value = num_value
                elseif value == "true" then
                    value = true
                elseif value == "false" then
                    value = false
                end
                
                config.set(section, key, value)
            end
        end
        file:close()
        return true
    else
        return false
    end
end

-- 保存配置文件
function config.save(filename)
    filename = filename or "config.txt"
    local file = io.open(filename, "w")
    if file then
        for section_name, section in pairs(config) do
            if type(section) == "table" and section_name ~= "get" and section_name ~= "set" and section_name ~= "load" and section_name ~= "save" then
                for key, value in pairs(section) do
                    file:write(section_name .. "." .. key .. "=" .. tostring(value) .. "\n")
                end
            end
        end
        file:close()
        return true
    else
        return false
    end
end

return config
