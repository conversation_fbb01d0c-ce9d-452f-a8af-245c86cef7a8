-- MUD客户端启动脚本
-- 编码: GB2312

-- 检查luasocket是否可用
local function check_luasocket()
    local success, socket = pcall(require, "socket")
    if not success then
        print("错误: 无法加载luasocket")
        print("请确保luasocket已正确安装")
        return false
    end
    print("luasocket加载成功")
    return true
end

-- 主函数
function main()
    print("=== MUD客户端 ===")
    print("版本: 1.0")
    print("编码: GB2312")
    print("")

    -- 检查依赖（不强制要求luasocket）
    check_luasocket()

    -- 加载客户端
    local success, client = pcall(require, "mudclient")
    if not success then
        print("错误: 无法加载客户端模块")
        print(client)
        return
    end

    -- 启动客户端
    client.StartClient()
end

-- 运行主函数
main()
