-- 测试luasocket是否可用
-- 编码: GB2312

print("测试luasocket...")

local success, socket = pcall(require, "socket")
if success then
    print("luasocket加载成功!")
    print("版本: " .. (socket._VERSION or "未知"))
    
    -- 测试创建TCP连接
    local tcp = socket.tcp()
    if tcp then
        print("TCP socket创建成功")
        tcp:close()
    else
        print("TCP socket创建失败")
    end
else
    print("luasocket加载失败: " .. tostring(socket))
    print("")
    print("可能的解决方案:")
    print("1. 下载luasocket 32位版本")
    print("2. 将socket目录放到当前目录下")
    print("3. 或者将socket.dll和mime.dll放到当前目录")
end
