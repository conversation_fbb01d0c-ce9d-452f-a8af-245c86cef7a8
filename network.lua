-- 网络连接模块
-- 编码: GB2312
-- 使用Lua的io.popen调用系统命令实现网络功能

local network = {}

-- 全局变量
network.connected = false
network.host = ""
network.port = 0
network.process = nil
network.total_bytes = 0

-- 连接到服务器
function network.connect(host, port)
    if network.connected then
        print("已经连接到服务器")
        return true
    end
    
    host = host or "pkuxkx.net"
    port = port or 8080
    
    network.host = host
    network.port = port
    
    print("正在连接到 " .. host .. ":" .. port .. "...")
    
    -- 使用telnet命令连接（Windows系统）
    local cmd = "telnet " .. host .. " " .. port
    network.process = io.popen(cmd, "r+")
    
    if network.process then
        network.connected = true
        print("连接成功!")
        return true
    else
        print("连接失败")
        return false
    end
end

-- 断开连接
function network.disconnect()
    if network.process then
        network.process:close()
        network.process = nil
    end
    network.connected = false
    print("连接已断开")
end

-- 发送数据
function network.send(data)
    if not network.connected or not network.process then
        print("未连接到服务器")
        return false
    end
    
    if not data then
        return false
    end
    
    -- 确保命令以换行符结尾
    if not string.match(data, "\n$") then
        data = data .. "\n"
    end
    
    local success = network.process:write(data)
    if success then
        network.process:flush()
        return true
    else
        print("发送失败")
        return false
    end
end

-- 接收数据
function network.receive()
    if not network.connected or not network.process then
        return nil
    end
    
    local data = network.process:read("*l")
    if data then
        network.total_bytes = network.total_bytes + #data
        return data
    end
    
    return nil
end

-- 检查连接状态
function network.is_connected()
    return network.connected
end

-- 获取接收字节数
function network.get_bytes()
    return network.total_bytes
end

-- 兼容性函数
function Connect(host, port)
    return network.connect(host, port)
end

function Disconnect()
    return network.disconnect()
end

function Send(data)
    return network.send(data)
end

function IsConnect()
    return network.is_connected()
end

function GameBytes()
    return network.get_bytes()
end

return network
