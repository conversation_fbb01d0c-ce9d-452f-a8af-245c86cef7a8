-- 测试MUD客户端
-- 编码: GB2312

print("=== MUD客户端测试 ===")

-- 测试加载客户端模块
print("1. 测试加载客户端模块...")
local success, client = pcall(require, "mudclient")
if success then
    print("   ✓ 客户端模块加载成功")
else
    print("   ✗ 客户端模块加载失败: " .. tostring(client))
    return
end

-- 测试基本函数
print("2. 测试基本函数...")

-- 测试Echo函数
if Echo then
    Echo("   ✓ Echo函数可用")
else
    print("   ✗ Echo函数不可用")
end

-- 测试Print函数
if Print then
    Print("Print函数可用")
    print("   ✓ Print函数可用")
else
    print("   ✗ Print函数不可用")
end

-- 测试连接函数
print("3. 测试连接函数...")
if Connect then
    print("   ✓ Connect函数可用")
else
    print("   ✗ Connect函数不可用")
end

if Disconnect then
    print("   ✓ Disconnect函数可用")
else
    print("   ✗ Disconnect函数不可用")
end

if IsConnect then
    print("   ✓ IsConnect函数可用")
    print("   当前连接状态: " .. tostring(IsConnect()))
else
    print("   ✗ IsConnect函数不可用")
end

-- 测试发送函数
if Send then
    print("   ✓ Send函数可用")
else
    print("   ✗ Send函数不可用")
end

-- 测试字节计数函数
if GameBytes then
    print("   ✓ GameBytes函数可用")
    print("   当前接收字节数: " .. GameBytes())
else
    print("   ✗ GameBytes函数不可用")
end

print("")
print("测试完成!")
print("")
print("如果所有测试都通过，可以运行以下命令启动客户端:")
print("  lua.exe start.lua")
print("或者:")
print("  lua.exe mudclient.lua")
