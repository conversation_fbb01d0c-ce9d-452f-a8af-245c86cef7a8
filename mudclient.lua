-- MUD客户端主程序
-- 编码: GB2312

-- 尝试加载luasocket，如果失败则使用备用方案
local socket = nil
local use_luasocket = false

local success, sock = pcall(require, "socket")
if success then
    socket = sock
    use_luasocket = true
    print("使用luasocket网络库")
else
    print("luasocket不可用，使用备用网络方案")
end

-- 客户端对象
local client = {}
client.connection = nil
client.connected = false
client.host = "pkuxkx.net"
client.port = 8080
client.receive_buffer = ""
client.total_bytes = 0
client.running = true

-- 连接到MUD服务器
function Connect(host, port)
    if client.connected then
        Echo("已经连接到服务器")
        return true
    end
    
    host = host or client.host
    port = port or client.port
    
    client.host = host
    client.port = port
    
    Echo("正在连接到 " .. host .. ":" .. port .. "...")
    
    if use_luasocket then
        -- 使用luasocket连接
        client.connection = socket.tcp()
        if not client.connection then
            Echo("创建socket失败")
            return false
        end
        
        client.connection:settimeout(10)
        
        local result, err = client.connection:connect(host, port)
        if not result then
            Echo("连接失败: " .. (err or "未知错误"))
            client.connection:close()
            client.connection = nil
            return false
        end
        
        client.connection:settimeout(0)
        client.connected = true
        Echo("连接成功!")
        return true
    else
        -- 备用连接方案（模拟）
        client.connected = true
        Echo("连接成功! (模拟模式)")
        return true
    end
end

-- 断开连接
function Disconnect()
    if use_luasocket and client.connection then
        client.connection:close()
        client.connection = nil
    end
    client.connected = false
    client.receive_buffer = ""
    Echo("连接已断开")
end

-- 检查连接状态
function IsConnect()
    return client.connected
end

-- 发送数据到服务器
function Send(data)
    if not client.connected then
        Echo("未连接到服务器")
        return false
    end
    
    if not data then
        return false
    end
    
    if not string.match(data, "\n$") then
        data = data .. "\n"
    end
    
    if use_luasocket and client.connection then
        local result, err = client.connection:send(data)
        if not result then
            Echo("发送失败: " .. (err or "未知错误"))
            if err == "closed" then
                client.connected = false
            end
            return false
        end
        return true
    else
        -- 模拟发送
        Echo("发送: " .. string.gsub(data, "\n", ""))
        return true
    end
end

-- 接收数据
function ReceiveData()
    if not client.connected then
        return nil
    end
    
    if use_luasocket and client.connection then
        local data, err = client.connection:receive("*a")
        
        if err == "closed" then
            Echo("服务器关闭了连接")
            client.connected = false
            return nil
        elseif err == "timeout" then
            return ""
        elseif err then
            Echo("接收数据错误: " .. err)
            return nil
        end
        
        if data and #data > 0 then
            client.total_bytes = client.total_bytes + #data
            return data
        end
        return ""
    else
        -- 模拟接收数据
        return ""
    end
end

-- 获取接收的总字节数
function GameBytes()
    return client.total_bytes
end

-- 处理接收到的数据
function ProcessReceiveData()
    local data = ReceiveData()
    if not data then
        return
    end
    
    if #data > 0 then
        client.receive_buffer = client.receive_buffer .. data
        
        -- 按行分割数据
        while true do
            local line_end = string.find(client.receive_buffer, "\n")
            if not line_end then
                break
            end
            
            local line = string.sub(client.receive_buffer, 1, line_end - 1)
            line = string.gsub(line, "\r", "")
            
            client.receive_buffer = string.sub(client.receive_buffer, line_end + 1)
            
            -- 调用接收处理函数
            if OnReceive then
                OnReceive(line, line)
            else
                print("收到: " .. line)
            end
        end
    end
end

-- 显示文本
function Echo(text)
    if text then
        print(text)
    end
end

-- 调试输出
function Print(text)
    if text then
        print("[DEBUG] " .. text)
    end
end

-- 处理用户输入
function ProcessInput()
    Echo("请输入命令:")
    local input = io.read()
    
    if input then
        if input == "quit" or input == "exit" then
            client.running = false
            return
        elseif input == "connect" then
            Connect()
            return
        elseif input == "disconnect" then
            Disconnect()
            return
        elseif input == "help" then
            Echo("可用命令:")
            Echo("  connect    - 连接到服务器")
            Echo("  disconnect - 断开连接")
            Echo("  quit/exit  - 退出程序")
            Echo("  help       - 显示帮助")
            return
        end
        
        -- 调用发送处理函数
        if OnSend then
            local result = OnSend(input)
            if result ~= false then
                Send(input)
            end
        else
            Send(input)
        end
    end
end

-- 主循环
function MainLoop()
    Echo("=== MUD客户端 ===")
    Echo("版本: 1.0")
    Echo("编码: GB2312")
    if use_luasocket then
        Echo("网络: luasocket")
    else
        Echo("网络: 模拟模式")
    end
    Echo("")
    Echo("输入 'help' 查看可用命令")
    Echo("")
    
    while client.running do
        ProcessInput()
        
        -- 处理网络数据
        if client.connected then
            ProcessReceiveData()
        end
        
        -- 短暂休眠
        if use_luasocket and socket.sleep then
            socket.sleep(0.01)
        end
    end
    
    if client.connected then
        Disconnect()
    end
    
    Echo("程序退出")
end

-- 加载脚本系统
function LoadScripts()
    Echo("正在加载脚本系统...")
    
    local success, err = pcall(function()
        dofile("sc/main.lua")
    end)
    
    if not success then
        Echo("加载脚本失败: " .. (err or "未知错误"))
        Echo("将以基础模式运行")
    else
        Echo("脚本系统加载成功")
    end
end

-- 程序入口
function StartClient()
    Echo("正在初始化MUD客户端...")
    
    -- 加载脚本系统
    LoadScripts()
    
    -- 启动主循环
    MainLoop()
end

-- 如果直接运行此文件，启动客户端
if arg and arg[0] and string.match(arg[0], "mudclient%.lua$") then
    StartClient()
end

return client
