-- MUD瀹㈡埛绔�鏍稿績缃戠粶妯″潡
-- 缂栫爜: GB2312
-- 浣跨敤luasocket瀹炵幇涓嶮UD鏈嶅姟鍣ㄧ殑缃戠粶閫氫俊

local socket = require("socket")

-- 鍏ㄥ眬鍙橀噺
local client = {}
client.connection = nil
client.connected = false
client.host = ""
client.port = 0
client.receive_buffer = ""
client.total_bytes = 0

-- 缂栫爜杞�鎹㈠嚱鏁帮紙GB2312 <-> UTF-8锛�
local function gb2312_to_utf8(str)
    -- 杩欓噷闇€瑕佹牴鎹�瀹為檯鎯呭喌瀹炵幇缂栫爜杞�鎹�
    -- 鏆傛椂鐩存帴杩斿洖鍘熷瓧绗︿覆
    return str
end

local function utf8_to_gb2312(str)
    -- 杩欓噷闇€瑕佹牴鎹�瀹為檯鎯呭喌瀹炵幇缂栫爜杞�鎹�
    -- 鏆傛椂鐩存帴杩斿洖鍘熷瓧绗︿覆
    return str
end

-- 杩炴帴鍒癕UD鏈嶅姟鍣�
function Connect(host, port)
    if client.connected then
        print("宸茬粡杩炴帴鍒版湇鍔″櫒")
        return true
    end
    
    host = host or client.host or "localhost"
    port = port or client.port or 23
    
    client.host = host
    client.port = port
    
    print("姝ｅ湪杩炴帴鍒� " .. host .. ":" .. port .. "...")
    
    client.connection = socket.tcp()
    if not client.connection then
        print("鍒涘缓socket澶辫触")
        return false
    end
    
    -- 璁剧疆瓒呮椂
    client.connection:settimeout(10)
    
    local result, err = client.connection:connect(host, port)
    if not result then
        print("杩炴帴澶辫触: " .. (err or "鏈�鐭ラ敊璇�"))
        client.connection:close()
        client.connection = nil
        return false
    end
    
    -- 璁剧疆涓洪潪闃诲�炴ā寮�
    client.connection:settimeout(0)
    client.connected = true
    
    print("杩炴帴鎴愬姛!")
    return true
end

-- 鏂�寮€杩炴帴
function Disconnect()
    if client.connection then
        client.connection:close()
        client.connection = nil
    end
    client.connected = false
    client.receive_buffer = ""
    print("杩炴帴宸叉柇寮€")
end

-- 妫€鏌ヨ繛鎺ョ姸鎬�
function IsConnect()
    return client.connected and client.connection ~= nil
end

-- 鍙戦€佹暟鎹�鍒版湇鍔″櫒
function Send(data)
    if not client.connected or not client.connection then
        print("鏈�杩炴帴鍒版湇鍔″櫒")
        return false
    end
    
    if not data then
        return false
    end
    
    -- 纭�淇濆懡浠や互鎹㈣�岀�︾粨灏�
    if not string.match(data, "\n$") then
        data = data .. "\n"
    end
    
    -- 杞�鎹㈢紪鐮�
    data = utf8_to_gb2312(data)
    
    local result, err = client.connection:send(data)
    if not result then
        print("鍙戦€佸け璐�: " .. (err or "鏈�鐭ラ敊璇�"))
        if err == "closed" then
            client.connected = false
        end
        return false
    end
    
    return true
end

-- 鎺ユ敹鏁版嵁
function ReceiveData()
    if not client.connected or not client.connection then
        return nil
    end
    
    local data, err = client.connection:receive("*a")
    
    if err == "closed" then
        print("鏈嶅姟鍣ㄥ叧闂�浜嗚繛鎺�")
        client.connected = false
        return nil
    elseif err == "timeout" then
        -- 瓒呮椂鏄�姝ｅ父鐨勶紝琛ㄧず娌℃湁鏁版嵁
        return ""
    elseif err then
        print("鎺ユ敹鏁版嵁閿欒��: " .. err)
        return nil
    end
    
    if data and #data > 0 then
        -- 杞�鎹㈢紪鐮�
        data = gb2312_to_utf8(data)
        client.total_bytes = client.total_bytes + #data
        client.receive_buffer = client.receive_buffer .. data
        return data
    end
    
    return ""
end

-- 鑾峰彇鎺ユ敹鐨勬€诲瓧鑺傛暟
function GameBytes()
    return client.total_bytes
end

-- 澶勭悊鎺ユ敹鍒扮殑鏁版嵁
function ProcessReceiveData()
    local data = ReceiveData()
    if not data then
        return
    end
    
    if #data > 0 then
        -- 鎸夎�屽垎鍓叉暟鎹�
        local lines = {}
        local buffer = client.receive_buffer
        
        -- 鏌ユ壘瀹屾暣鐨勮��
        while true do
            local line_end = string.find(buffer, "\n")
            if not line_end then
                break
            end
            
            local line = string.sub(buffer, 1, line_end - 1)
            -- 绉婚櫎鍥炶溅绗�
            line = string.gsub(line, "\r", "")
            
            table.insert(lines, line)
            buffer = string.sub(buffer, line_end + 1)
        end
        
        client.receive_buffer = buffer
        
        -- 澶勭悊姣忎竴琛�
        for _, line in ipairs(lines) do
            if OnReceive then
                -- 璋冪敤鐜版湁鐨勬帴鏀跺�勭悊鍑芥暟
                OnReceive(line, line)
            else
                -- 鐩存帴鏄剧ず
                print(line)
            end
        end
    end
end

-- 涓诲惊鐜�澶勭悊鍑芥暟
function ProcessNetwork()
    if client.connected then
        ProcessReceiveData()
    end
end

-- 璁剧疆鏈嶅姟鍣ㄥ湴鍧€
function SetServer(host, port)
    client.host = host
    client.port = port
end

-- 鑾峰彇杩炴帴淇℃伅
function GetConnectionInfo()
    return {
        host = client.host,
        port = client.port,
        connected = client.connected,
        total_bytes = client.total_bytes
    }
end

-- 鍏煎�规€у嚱鏁�
function Echo(text)
    if text then
        print(text)
    end
end

function Print(text)
    if text then
        print("[DEBUG] " .. text)
    end
end

-- 瀵煎嚭瀹㈡埛绔�瀵硅薄
return client
